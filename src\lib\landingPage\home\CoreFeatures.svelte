<script>
    import { H2, P1, P2 } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    import SignUpButton from '$lib/landingPage/SignUpButton.svelte';
	import { innerWidth } from 'svelte/reactivity/window';

    // Import images
    import qbImage from '$lib/assets/home/<USER>';
    import simulationImage from '$lib/assets/home/<USER>';
    import dashboardImage from '$lib/assets/home/<USER>';
    import vocabToolImage from '$lib/assets/home/<USER>';

    let {
        sectionLabel = "Core Features",
        headline = "Everything you need to get your 1600",
        subheadline = "Master the Digital SAT with our comprehensive platform designed by a perfect scorer.",
        features = [
            {
                id: "question-bank",
                color: "var(--rose)",
                icon: '<img src="/question-bank-icon.svg" alt="Question Bank" width="32" height="32" />',
                title: "Question Bank",
                description: "Practice with thousands of high-quality questions organized by topic and difficulty, with detailed explanations for every answer.",
                image: qbImage
            },
            {
                id: "simulations",
                color: "var(--tangerine)",
                icon: '<img src="/simulation-icon.svg" alt="Simulations" width="32" height="32" />',
                title: "Simulations",
                description: "Take realistic Digital SAT full-length practice test. Get instant scoring and detailed breakdowns.",
                image: simulationImage
            },
            {
                id: "dashboard",
                color: "var(--sky-blue)",
                icon: '<img src="/dashboard-icon.svg" alt="Dashboard" width="32" height="32" />',
                title: "Dashboard",
                description: "Track your progress, set goals, and stay motivated with our dashboard.",
                image: dashboardImage
            },
            {
                id: "vocab-tool",
                color: "var(--aquamarine)",
                icon: '<img src="/vocab-tool-icon.svg" alt="AI Vocabulary Tool" width="32" height="32" />',
                title: "AI Vocab Tool",
                description: "Build your vocabulary with state-of-the-art spaced repetition algorithm that helps you learn more words in less time.",
                image: vocabToolImage
            },
        ],
        primaryButton = "See Them in Action",
        secondaryButton = "All Features"
    } = $props();

    let activeTab = $state(features[0].id);

    function setActiveTab(tabId) {
        activeTab = tabId;
    }
</script>

<SectionWrapper --bg-color="var(--purple)" --padding-top="8rem" --padding-bottom="8rem">
<div class="features-section">
    <div class="features-header">
        <P1>{sectionLabel}</P1>
        <H2>{@html headline}</H2>
        <P1>{subheadline}</P1>
    </div>

    <!-- Feature Tabs Navigation -->
    <div class="feature-tabs">
        {#each features as feature}
            <button
                class="feature-tab {activeTab === feature.id ? 'active' : ''}"
                onclick={() => setActiveTab(feature.id)}
                style:border-color={feature.color}
            >
                <div class="tab-icon" style:background-color={feature.color}>
                    {@html feature.icon}
                </div>
                {#if innerWidth.current > 768}
                    <P2 isBold>{feature.title}</P2>
                {/if}
            </button>
        {/each}
    </div>

    <!-- Feature Content Carousel -->
    <div class="feature-carousel">
        {#each features as feature}
            <div class="feature-content-panel {activeTab === feature.id ? 'active' : ''}" id="tab-content-{feature.id}">
                <div class="feature-left">
                    <div class="feature-info">
                        <div class="feature-info-content">
                            <H2>{feature.title}</H2>
                            <P1>{feature.description}</P1>
                        </div>
                    </div>
                    <div class="feature-action">
                        <SignUpButton>Try {feature.title}</SignUpButton>
                    </div>
                </div>
                <div class="feature-image-container">
                    <enhanced:img
                        src={feature.image}
                        alt="{feature.title} Demo"
                        class="feature-image"
                        loading="lazy"
                    />
                </div>
            </div>
        {/each}
    </div>
</div>
</SectionWrapper>

<style>
    /* Features Section */
    .features-section {
        max-width: 90rem;
        width: 100%;
        text-align: center;
        position: relative;
    }

    .features-header {
        margin: 0 auto 5rem auto;
        position: relative;
        padding: 2rem;
        background: var(--white);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
        width: fit-content;
    }

    /* Feature Tabs Navigation */
    .feature-tabs {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .feature-tab {
        background: var(--white);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 1rem 1.5rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        color: var(--pitch-black);
        transition: all 0.2s ease;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        position: relative;
    }

    .feature-tab:hover {
        transform: translateY(-0.125rem);
        box-shadow: 0.375rem 0.375rem 0 var(--pitch-black);
    }

    .feature-tab.active {
        background: var(--yellow);
        transform: translateY(-0.25rem);
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
        border-width: 0.375rem;
    }

    .tab-icon {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 0.125rem solid var(--pitch-black);
    }

    /* Feature Carousel */
    .feature-carousel {
        position: relative;
        overflow: hidden;
        border-radius: 1rem;
        height: 30rem;
        border: 0.25rem solid var(--pitch-black);
        box-shadow: 1rem 1rem 0 var(--pitch-black);
        background: var(--white);
    }

    .feature-left {
        text-align: start;
    }

    .feature-content-panel {
        display: none;
        flex-direction: row;
        height: 100%;
        padding: 2rem;
        gap: 2rem;
        align-items: stretch;
        justify-content: space-between;
    }

    .feature-content-panel.active {
        display: flex;
    }

    .feature-image-container {
        flex: 1 0 0;
        aspect-ratio: 16 / 9;
        border-radius: 1rem;
        border: 1px solid var(--pitch-black);
        transition: transform 0.2s ease;
        overflow: hidden;
        background: var(--white);
    }

    .feature-image {
        object-fit: cover;
        object-position: center;
    }

    .feature-info {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
    }

    .feature-info-content {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        width: 100%;
    }

    .feature-action {
        text-align: left;
        margin-top: 2rem;
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .features-header {
            margin-bottom: 3rem;
        }

        .feature-tabs {
            gap: 0.5rem;
            margin-bottom: 3rem;
        }

        .feature-tab {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
        }

        .tab-icon {
            width: 2rem;
            height: 2rem;
        }

        .feature-content-panel {
            min-height: auto;
            padding: 1.5rem;
            flex-direction: column;
        }

        .feature-left {
            text-align: center;
        }

        .feature-info {
            align-items: center;
            justify-content: center;
        }

        .feature-action {
            margin-bottom: 1.5rem;
            margin-top: 0;
            text-align: center;
        }
    }
</style>
